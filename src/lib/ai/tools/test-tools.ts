import { tool } from "ai";
import { z } from "zod/v4";

/**
 * Simple test tool to verify AI agent functionality
 */
export const testTool = () =>
  tool({
    description: "Test tool to verify AI agent is working correctly",
    parameters: z.object({
      message: z.string().describe("Test message to echo back"),
    }),
    execute: async ({ message }) => {
      return `AI Agent Test Successful! You sent: "${message}"`;
    },
  });

/**
 * Role verification tool
 */
export const checkUserRole = () =>
  tool({
    description: "Check what role the current user has and what tools are available",
    parameters: z.object({}),
    execute: async () => {
      return JSON.stringify({
        message: "Role check completed",
        note: "This tool should show different capabilities based on user role",
        availableFeatures: [
          "Basic chat functionality",
          "Knowledge base access",
          "Role-specific tools based on user permissions",
        ],
      }, null, 2);
    },
  });
