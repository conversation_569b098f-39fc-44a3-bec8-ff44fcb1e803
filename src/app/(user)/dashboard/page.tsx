import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { ContractorDashboard } from "@/components/contractor/contractor-dashboard";
import { StatsOverview } from "@/components/dashboard/stats-overview";
import { HomeownerDashboard } from "@/components/homeowner/homeowner-dashboard";
import { PageLayout } from "@/components/page-layout";
import { auth } from "@/lib/auth";

export default async function DashboardPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  if (!session?.user.onboardingComplete) {
    redirect("/onboarding");
  }

  // Determine if user is a homeowner or professional
  const isProfessional = session?.user?.role === "contractor";

  return (
    <PageLayout title={isProfessional ? "Contractor Dashboard" : "Dashboard"}>
      {/* Mobile-first layout: Jobs first, then stats */}
      <div className="flex flex-col space-y-4 sm:space-y-8">
        {/* Mobile: Jobs first for immediate action, Desktop: Stats first */}
        <div className="order-1 sm:order-2">
          {isProfessional ? <ContractorDashboard /> : <HomeownerDashboard />}
        </div>

        {/* Mobile: Stats second, Desktop: Stats first */}
        <div className="order-2 sm:order-1">
          <StatsOverview isProfessional={isProfessional} />
        </div>
      </div>
    </PageLayout>
  );
}
